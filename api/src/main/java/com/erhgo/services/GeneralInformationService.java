package com.erhgo.services;

import com.erhgo.domain.classifications.erhgooccupation.ErhgoOccupation;
import com.erhgo.domain.enums.ContactTime;
import com.erhgo.domain.enums.DriverLicence;
import com.erhgo.domain.enums.Situation;
import com.erhgo.domain.exceptions.EntityNotFoundException;
import com.erhgo.domain.exceptions.GenericTechnicalException;
import com.erhgo.domain.exceptions.UserNotAllowedForEntity;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.*;
import com.erhgo.domain.userprofile.criteria.UserCriteriaValue;
import com.erhgo.domain.userprofile.event.*;
import com.erhgo.domain.utils.EventPublisherUtils;
import com.erhgo.openapi.dto.*;
import com.erhgo.repositories.CriteriaRepository;
import com.erhgo.repositories.GeneralInformationRepository;
import com.erhgo.repositories.RecruiterRepository;
import com.erhgo.repositories.UserProfileRepository;
import com.erhgo.repositories.dto.UserProfileProgress;
import com.erhgo.security.AuthorizeExpression;
import com.erhgo.security.Role;
import com.erhgo.services.dtobuilder.GeneralInformationDTOBuilder;
import com.erhgo.services.dtobuilder.MailingUserDTO;
import com.erhgo.services.dtobuilder.PageDTOBuilder;
import com.erhgo.services.dtobuilder.UserProfileDTOBuilder;
import com.erhgo.services.keycloak.KeycloakService;
import com.erhgo.services.keycloak.UserRepresentation;
import com.erhgo.services.mailing.MailingListService;
import com.erhgo.utils.KeycloakUtils;
import com.erhgo.utils.PaginatedDataUtils;
import com.erhgo.utils.StringUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.opencsv.CSVWriter;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.OutputStreamWriter;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.opencsv.ICSVWriter.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class GeneralInformationService {
    public static final String NONE = "Aucun";
    private final GeneralInformationRepository repository;
    private final RecruiterRepository recruiterRepository;
    private final UserProfileRepository userProfileRepository;
    private final KeycloakService keycloakService;
    private final MailingListService mailingListService;
    private final SecurityService securityService;
    private final GeneralInformationDTOBuilder generalInformationDTOBuilder;
    private final UserProfileDTOBuilder userProfileDTOBuilder;
    private final CriteriaRepository criteriaRepository;

    private static final int USERS_FOR_GROUPS_NAME_INDEX = 3;
    private static final int USERS_FOR_GROUPS_FIRST_NAME_INDEX = 2;
    private static final int USERS_FOR_GROUPS_EMAIL_INDEX = 4;
    private static final int USERS_FOR_GROUPS_PHONE_INDEX = 9;

    private GeneralInformation getGeneralInformationByProfileUuid(UUID userProfileUuid) {
        return repository.findByUserProfileUuid(userProfileUuid);
    }

    private UserProfile getUserProfileOrThrow(String userId) {
        return userProfileRepository.findByUserId(userId).orElseThrow(() -> new EntityNotFoundException(userId, UserProfile.class));
    }

    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public GeneralInformation getGeneralInformationByUserId(String userId) {
        var generalInformation = getGeneralInformationByProfileUuid(getUserProfileOrThrow(userId).uuid());
        if (generalInformation == null) {
            var profile = getUserProfileOrThrow(userId);
            generalInformation = GeneralInformation.builder().userId(userId).userProfile(profile).build();
            profile.generalInformation(generalInformation);
        }
        return generalInformation;
    }

    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void updateCity(SetUserCityCommandDTO command) {
        var userId = securityService.getAuthenticatedUserId();
        var generalInformation = getGeneralInformationByUserId(userId);
        var location = command.getLocation();
        generalInformation.setLocation(location == null ? null : buildLocationFromDTO(location));
        repository.save(generalInformation);
        EventPublisherUtils.publish(new UserLocationUpdatedEvent(generalInformation.getUserProfile()));
    }


    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void updateSalary(SetUserSalaryCommandDTO dto) {
        var generalInformation = getGeneralInformationByUserId(securityService.getAuthenticatedUserId());
        generalInformation.setSalary(dto.getSalary());
        EventPublisherUtils.publish(new UserSalaryUpdatedEvent(generalInformation.getUserProfile()));
        repository.save(generalInformation);
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void updateSituation(SetUserSituationCommandDTO dto) {
        var generalInformation = getGeneralInformationByUserId(securityService.getAuthenticatedUserId());
        generalInformation.setSituation(Situation.valueOf(dto.getSituation().name()));
        generalInformation.setDelayInMonth(dto.getDelayInMonth());
        EventPublisherUtils.publish(new UserSituationUpdatedEvent(generalInformation.getUserProfile()));
    }

    @Transactional
    @RolesAllowed(Role.CANDIDATE)
    public void updatePhoneNumber(SetUserPhoneNumberCommandDTO dto) {
        var userId = securityService.getAuthenticatedUserId();
        var generalInformation = getGeneralInformationByUserId(userId);
        generalInformation.setPhoneNumber(dto.getPhoneNumber());
        EventPublisherUtils.publish(new UserPhoneNumberUpdatedEvent(generalInformation.getUserProfile()));
    }


    @Transactional
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_WRITE)
    public void updateContactInfo(String userId, SaveUserContactInfoCommandDTO saveUserContactInfoCommandDTO) {
        var generalInformation = getGeneralInformationByUserId(userId);
        generalInformation.setPhoneNumber(saveUserContactInfoCommandDTO.getPhoneNumber());
        Optional.ofNullable(saveUserContactInfoCommandDTO.getContactTime()).map(ContactTimeDTO::name).map(ContactTime::valueOf).ifPresent(generalInformation::setContactTime);
        generalInformation.setBirthDate(saveUserContactInfoCommandDTO.getBirthDate());
        var userLocation = Optional.ofNullable(saveUserContactInfoCommandDTO.getLocation());
        generalInformation.setSalary(saveUserContactInfoCommandDTO.getSalary());
        generalInformation.setLocation(userLocation.map(this::buildLocationFromDTO).orElse(null));
        userLocation.map(LocationDTO::getRadiusInKm).ifPresent(generalInformation::setRadiusInKm);
        generalInformation.setSituation(saveUserContactInfoCommandDTO.getSituation() == null ? null : Situation.valueOf(saveUserContactInfoCommandDTO.getSituation().name()));
        generalInformation.setDelayInMonth(saveUserContactInfoCommandDTO.getDelayInMonth());
        if (!securityService.isCandidate()) {
            generalInformation.getUserProfile().source(saveUserContactInfoCommandDTO.getSource());
        }

        repository.save(generalInformation);

        updateEmailRelatedDataIfPresent(generalInformation.getUserProfile(), saveUserContactInfoCommandDTO, userLocation, !generalInformation.getUserProfile().isWaitingForBOTermination());
        EventPublisherUtils.publish(new UserContactInfoUpdatedEvent(generalInformation.getUserProfile()));
    }

    private void updateEmailRelatedDataIfPresent(UserProfile userProfile,
                                                 SaveUserContactInfoCommandDTO saveUserContactInfoCommandDTO,
                                                 Optional<LocationDTO> userLocation,
                                                 boolean isUserConfirmed) {
        var userEmail = getUserEmailOptional(userProfile.userId());
        userEmail.ifPresent(email -> {
            var commandEmail = saveUserContactInfoCommandDTO.getEmail();
            var emailUpdated = !Strings.isNullOrEmpty(commandEmail) && !email.equals(commandEmail);
            keycloakService.updateFOEmailAndUsername(userProfile.userId(), commandEmail, saveUserContactInfoCommandDTO.getFirstName(), saveUserContactInfoCommandDTO.getLastName());
            if (isUserConfirmed) {
                if (emailUpdated) {
                    mailingListService.processFOEmailUpdate(email, commandEmail);
                }
                if (saveUserContactInfoCommandDTO.getReceiveJobOfferEmails() != null) {
                    userProfile.setJobOfferOptOut(saveUserContactInfoCommandDTO.getReceiveJobOfferEmails());
                    EventPublisherUtils.publish(new UserJobOffersOptInUpdatedEvent(userProfile));
                }
                EventPublisherUtils.publish(new UserPhoneNumberUpdatedEvent(userProfile));
                EventPublisherUtils.publish(new UserNameUpdatedEvent(userProfile));
                userLocation.ifPresent(location -> EventPublisherUtils.publish(new UserLocationUpdatedEvent(userProfile)));
            }
        });
    }

    private Optional<String> getUserEmailOptional(String userId) {
        return keycloakService.getFrontOfficeUserProfile(userId).map(UserRepresentation::getEmail)
                .filter(Predicate.not(StringUtils::isTemporaryEmail));
    }


    @Transactional(readOnly = true)
    @PreAuthorize(AuthorizeExpression.USER.USER_PROFILE_READ)
    public UserContactInfoDTO getContactInfo(String userId) {
        var generalInformation = getGeneralInformationByUserId(userId);
        return new UserContactInfoDTO()
                .id(userId)
                .contactInformation(
                        generalInformationDTOBuilder.buildGeneralInformationDTO(
                                generalInformation,
                                keycloakService.getFrontOfficeUserProfile(userId).orElseThrow())
                );
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public UserPageDTO searchFrontOfficeUsers(String search, int page, int size) {
        var userPage = keycloakService
                .searchFrontOfficeUsersPaginated(search, page, size)
                .map(keycloakUser -> generalInformationDTOBuilder.buildUserSummary(keycloakUser, FoUserSummaryDTO.class));

        var dtoForId = userPage.stream().collect(Collectors.toMap(UserSummaryDTO::getId, Function.identity()));
        var userProfiles = userProfileRepository.getUserProfilesProgress(
                dtoForId.keySet(),
                Collections.emptyList(),
                securityService.isAdmin(),
                false,
                null);

        userProfiles.forEach(x -> {
            var foUserSummaryDTO = dtoForId.get(x.getUserId());
            foUserSummaryDTO.setUserProfileProgress(UserProfileDTOBuilder.buildUserProfileProgress(x));
            foUserSummaryDTO.setTransactionalBlacklisted(x.getUserProfile().getJobOfferOptOut());
            foUserSummaryDTO.setCreatedAt(Optional.ofNullable(x.getUserProfile().getCreatedDate()).map(d -> d.toInstant().atOffset(ZoneOffset.UTC)).orElse(null));
        });

        return PageDTOBuilder.buildUserPage(userPage);
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public UserByGroupPageDTO searchFrontOfficeUsersByGroup(Collection<String> organizationCodes, Integer size, Integer page, Boolean strictOrganizationFilter, String postcode, String search) {
        var roles = BooleanUtils.isTrue(strictOrganizationFilter) ?
                new HashSet<>(organizationCodes) :
                organizationCodes.stream()
                        .map(keycloakService::getRolesForGroup)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet());
        Page<UserProfileProgress> users;
        if (Strings.isNullOrEmpty(search)) {
            users = userProfileRepository.getUserProfilesProgressByChannels(
                    roles,
                    securityService.isAdmin(),
                    PageRequest.of(page, size),
                    postcode
            );
        } else {
            // If there is a query, we break pagination: all userId matching query are retrieved
            var userIdOfChannelMatchingQuery = computeUserIdsInChannelsMatchingQuery(roles, search);

            var content = userProfileRepository.getUserProfilesProgress(
                    userIdOfChannelMatchingQuery,
                    roles,
                    securityService.isAdmin(),
                    false,
                    postcode);
            users = new PageImpl<>(content, Pageable.unpaged(), content.size());

        }
        return PageDTOBuilder.buildUserByGroupPage(users
                .map(x -> userProfileDTOBuilder.buildUserContactDtoWithUserProgress(x, roles)));
    }

    private Set<String> computeUserIdsInChannelsMatchingQuery(Set<String> roles, String query) {
        return userProfileRepository.findByAffectationsHistoryUserChannelChannelIn(roles)
                .stream()
                .map(u -> keycloakService.getFrontOfficeUserProfile(u.userId()))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .filter(u -> userMatchesQuery(u, query.toLowerCase()))
                .map(UserRepresentation::getId)
                .collect(Collectors.toSet());
    }

    private boolean userMatchesQuery(UserRepresentation u, String lowerCasedQuery) {
        return (u.getFirstName() != null && u.getFirstName().toLowerCase().contains(lowerCasedQuery))
                || (u.getLastName() != null && u.getLastName().toLowerCase().contains(lowerCasedQuery))
                || (u.getEmail().toLowerCase().contains(lowerCasedQuery));
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public List<UserSummaryDTO> getBackOfficeUsers() {
        return keycloakService
                .getBackOfficeUsers()
                .stream()
                .map(keycloakUser -> generalInformationDTOBuilder.buildUserSummary(keycloakUser, UserSummaryDTO.class))
                .toList();
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public void writeUsersCsv(OutputStreamWriter writer, List<String> usersId, boolean deanonymizedUser) {
        var listUserJobDating = new ArrayList<MailingUserDTO>();
        var jdListError = false;
        var jobListError = false;

        var isListSpecified = usersId != null && !usersId.isEmpty();
        if (!isListSpecified && !securityService.isAdmin()) {
            throw new UserNotAllowedForEntity("Non admin users cannot export all users");
        }
        var isGlobalExport = !isListSpecified;
        if (isGlobalExport) {
            try {
                listUserJobDating.addAll(PaginatedDataUtils.getPaginatedData(100L, mailingListService::getListUsersJobDating, 200L));
            } catch (GenericTechnicalException e) {
                log.error("error when fetching JD optin list users", e);
                jdListError = true;
            }
        }
        var organizationTitleForCode = StreamSupport.stream(recruiterRepository.findAll().spliterator(), false)
                .collect(Collectors.toMap(Recruiter::getCode, Recruiter::getTitle));

        var users = isListSpecified ?
                usersId.stream()
                        .map(keycloakService::getFrontOfficeUserProfile)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .toList()
                : keycloakService.findAllFrontOfficeUsers();

        var usersProfileProgress = userProfileRepository.getUserProfilesProgress(
                        isListSpecified ? usersId : Collections.emptyList(),
                        Collections.emptyList(),
                        securityService.isAdmin(),
                        !isListSpecified,
                        null)
                .stream()
                .collect(Collectors.toMap(UserProfileProgress::getUserId, Function.identity()));

        @SuppressWarnings("java:S3958")
        var usersStream = users.stream()
                .sorted(Comparator.comparing(UserRepresentation::getCreatedTimestamp, Comparator.nullsLast(Comparator.reverseOrder())))
                .map(userRepresentation -> buildUserExportLine(
                        isGlobalExport,
                        listUserJobDating,
                        userRepresentation,
                        Optional.ofNullable(usersProfileProgress.get(userRepresentation.getId())),
                        organizationTitleForCode,
                        deanonymizedUser)
                );

        try (var csvWriter = new CSVWriter(
                writer,
                StringUtils.CSV_FIELD_SEPARATOR,
                DEFAULT_QUOTE_CHARACTER,
                DEFAULT_ESCAPE_CHARACTER,
                DEFAULT_LINE_END)) {
            csvWriter.writeNext(buildExportHeaders(jobListError, jdListError, isGlobalExport, deanonymizedUser));
            csvWriter.writeAll(usersStream::iterator);
        } catch (IOException e) {
            log.error("Unable to generate CSV", e);
        }
    }

    private String[] buildExportHeaders(boolean jobListError, boolean jdListError, boolean isGlobalExport, boolean deanonymizedUser) {
        var headers = Lists.newArrayList("Identifiant", "Date de création", "Dernière connexion");
        if (securityService.isAdmin()) {
            headers.add("Prescripteur");
        }
        headers.addAll(List.of("Source", "Canaux", "Ville", "Code postal", "Étape d'inscription", "Taux complétude", "Situation", "Disponibilité", "Délai", "Salaire", "Permis", "Type de contrat", "Temps plein / Temps partiel", "Métier sélectionné", "Intitulé du métier", "Nombre d'expériences", "Compte handicap"));

        if (deanonymizedUser) {
            headers.add(USERS_FOR_GROUPS_FIRST_NAME_INDEX, "Prénom");
            headers.add(USERS_FOR_GROUPS_NAME_INDEX, "Nom");
        }
        if (!isAnonymizationRequired()) {
            if (deanonymizedUser) {
                headers.add(USERS_FOR_GROUPS_EMAIL_INDEX, "Adresse mail");
                headers.add(USERS_FOR_GROUPS_PHONE_INDEX, "Numéro de téléphone");
            }
            if (securityService.isAdminOrOTAdmin()) {
                headers.add("Emails transactionnels blacklistés");
            }
            if (isGlobalExport) {
                headers.add("Inscrit JD Lyon" + (jdListError ? " Inconnu : Erreur SendinBlue" : ""));
                headers.add("Inscrit offres d’emploi" + (jobListError ? " Inconnu : Erreur SendinBlue" : ""));
                headers.add("Notes sur le candidat");
            }
        }
        return headers.toArray(String[]::new);
    }

    private String[] buildUserExportLine(
            boolean isGlobalExport,
            List<MailingUserDTO> listUserJobDating,
            UserRepresentation userRepresentation,
            Optional<UserProfileProgress> userProfileProgress,
            Map<String, String> organizationTitleForCode,
            boolean deanonymizedUser
    ) {
        var jobDating = false;
        if (isGlobalExport) {
            jobDating = listUserJobDating.stream().anyMatch(u -> u.getEmail().equalsIgnoreCase(userRepresentation.getEmail()));
        }
        ArrayList<String> criteriaWorkingType = new ArrayList<>(
                Arrays.asList("REP-1-1", "REP-1-2", "REP-1-3"));
        ArrayList<String> criteriaWorkingTime = new ArrayList<>(
                Arrays.asList("REP-2-1", "REP-2-2"));
        var adminOrOTAdmin = securityService.isAdminOrOTAdmin();
        var undefinedLabel = "(Non défini)";
        var userProfile = userProfileProgress.map(UserProfileProgress::getUserProfile);
        var generalInformation = userProfile.map(UserProfile::generalInformation);
        var userRegistrationState = userProfile.map(UserProfile::userRegistrationState);
        var location = generalInformation.map(GeneralInformation::getLocation);
        var showEmail = adminOrOTAdmin || !userProfile.map(UserProfile::getJobOfferOptOut).orElse(false);
        var phoneLabel = generalInformation.map(u -> u.getPhoneLabel(adminOrOTAdmin)).orElse(undefinedLabel);
        var disponibility = (generalInformation.map(GeneralInformation::getSituation).orElse(null) == Situation.RESEARCHING
                || generalInformation.map(GeneralInformation::getSituation).orElse(null) == null)
                && generalInformation.map(GeneralInformation::getDelayInMonth).orElse(0) == 0 ? "De suite" : "Sous délai";

        var datas = Lists.newArrayList(
                userRepresentation.getId(),
                formatTimestamp(userRepresentation.getCreatedTimestamp()),
                formatTimestamp(generalInformation.map(GeneralInformation::getLastConnectionDate).orElse(null)));
        if (securityService.isAdmin()) {
            var prescriberCode = userProfile.map(up -> up.prescriber().channel());
            var prescriberName = prescriberCode.map(organizationTitleForCode::get).orElse("Inconnu");
            var prescriberLabel = prescriberCode.map(code -> "%s (%s)".formatted(prescriberName, code)).orElse(NONE);
            datas.add(prescriberLabel);
        }
        datas.addAll(List.of(
                userProfile.map(UserProfile::channelSourceType)
                        .map(UserChannel.ChannelSourceType::text)
                        .orElse(UserChannel.ChannelSourceType.UNKNOWN.text()),
                generalInformation.map(GeneralInformation::getChannels)
                        .map(channels -> joinOrganizationTitles(organizationTitleForCode, channels))
                        .orElse(""),
                location.map(Location::getCity).orElse(undefinedLabel),
                location.map(Location::getPostcode).orElse(undefinedLabel),
                formatUserRegistrationState(userProfileProgress),
                formatUserProgressProfile(userProfileProgress).orElse(undefinedLabel),
                generalInformation.map(GeneralInformation::getSituation).map(Situation::getText).orElse(undefinedLabel),
                disponibility,
                String.valueOf(generalInformation.map(GeneralInformation::getDelayInMonth).orElse(0)),
                generalInformation.map(GeneralInformation::getSalary).map("%d€"::formatted).orElse(undefinedLabel),
                generalInformation.map(GeneralInformation::getDriverLicence).map(DriverLicence::getText).orElse(undefinedLabel),
                userProfile.map(UserProfile::getUserCriteriaValues).map(c -> c.stream().filter(UserCriteriaValue::isSelected).filter(cr -> criteriaWorkingType.contains(cr.getValue().getCode())).map(cri -> cri.getValue().getTitleForBO()).collect(Collectors.joining(" / "))).orElse(undefinedLabel),
                userProfile.map(UserProfile::getUserCriteriaValues).map(c -> c.stream().filter(UserCriteriaValue::isSelected).filter(cr -> criteriaWorkingTime.contains(cr.getValue().getCode())).map(cri -> cri.getValue().getTitleForBO()).collect(Collectors.joining(" / "))).orElse(undefinedLabel),
                userRegistrationState.map(UserRegistrationState::getSelectedOccupation)
                        .map(ErhgoOccupation::getTitle)
                        .orElse(NONE),
                userRegistrationState.map(UserRegistrationState::getJobTitle)
                        .orElse(NONE),
                String.valueOf(userProfile.map(UserProfile::experiences).map(Set::size).orElse(0)),
                getHandicapAccountInfo(userProfile))
        );
        if (!isAnonymizationRequired() && deanonymizedUser) {
            datas.add(USERS_FOR_GROUPS_FIRST_NAME_INDEX, userRepresentation.getFirstName());
            datas.add(USERS_FOR_GROUPS_NAME_INDEX, userRepresentation.getLastName());
            datas.add(USERS_FOR_GROUPS_EMAIL_INDEX, showEmail ? userRepresentation.getEmail() : "(Désabonné)");
            datas.add(USERS_FOR_GROUPS_PHONE_INDEX, phoneLabel);
        }
        if (adminOrOTAdmin) {
            datas.add(getBooleanLabel(userProfile.map(UserProfile::getJobOfferOptOut)));
        }

        if (isGlobalExport) {
            datas.add(userProfile.map(UserProfile::getJobOfferOptOut).map(ok -> ok ? "Inscrit" : "Désinscrit").orElse("Inconnu"));
            datas.add(jobDating ? "Inscrit" : "");

            var notes = userProfile
                    .map(UserProfile::userNotes)
                    .map(n -> n.stream().map(u -> u.getUpdatedDate() + ": " + u.getContent())
                            .collect(Collectors.joining("\n---\n")))
                    .orElse("");
            datas.add(notes);
        }
        return datas.toArray(new String[]{});
    }

    @SuppressWarnings("java:S5411")
    private String getBooleanLabel(Optional<Boolean> bool) {
        return bool.map(b -> b ? "Oui" : "Non").orElse("(Non précisé)");
    }

    private String getHandicapAccountInfo(Optional<UserProfile> userProfile) {
        if (userProfile.isEmpty()) {
            return "Inconnu";
        }

        var profile = userProfile.get();
        var isFromHandicap = Optional.ofNullable(profile.getIsFromHandicap()).orElse(false);
        var handicapModeEnabled = Optional.ofNullable(profile.getHandicapModeEnabled()).orElse(false);

        if (isFromHandicap && handicapModeEnabled) {
            return "Origine + Actif";
        } else if (isFromHandicap) {
            return "Origine";
        } else if (handicapModeEnabled) {
            return "Actif";
        } else {
            return "Non";
        }
    }

    private boolean isAnonymizationRequired() {
        return !securityService.hasAuthenticatedUserAnyRole(Role.ODAS_ADMIN);
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public void exportHandicapUsersOnly(Writer writer) {
        log.info("Starting handicap users export");

        var users = keycloakService.getAllFrontOfficeUsers();
        var organizationTitleForCode = organizationRepository.findAll()
                .stream()
                .collect(Collectors.toMap(AbstractOrganization::getCode, AbstractOrganization::getTitle));

        var usersStream = users.stream()
                .sorted(Comparator.comparing(UserRepresentation::getCreatedTimestamp, Comparator.nullsLast(Comparator.reverseOrder())))
                .map(userRepresentation -> buildHandicapUserExportLine(userRepresentation, organizationTitleForCode))
                .toList();

        try (var csvWriter = new CSVWriter(
                writer,
                StringUtils.CSV_FIELD_SEPARATOR,
                DEFAULT_QUOTE_CHARACTER,
                DEFAULT_ESCAPE_CHARACTER,
                DEFAULT_LINE_END)) {
            csvWriter.writeNext(buildHandicapExportHeaders());
            csvWriter.writeAll(usersStream);
        } catch (IOException e) {
            log.error("Unable to generate handicap CSV", e);
        }
    }

    private String joinOrganizationTitles(Map<String, String> organizationTitleForCode, java.util.Set<String> c) {
        return c.stream()
                .map(organizationTitleForCode::get)
                // title is null means user is not associated to this channel
                .filter(Objects::nonNull)
                .sorted()
                .collect(Collectors.joining(", "));
    }

    private String formatUserRegistrationState(Optional<UserProfileProgress> user) {
        return user.map(x -> x.getUserProfile().userRegistrationState().getRegistrationStep().getLabel()).orElse("En erreur");
    }

    private Optional<String> formatUserProgressProfile(Optional<UserProfileProgress> user) {
        return user.map(userProfileProgress -> {
            var hasBehaviors = userProfileProgress.isHasBehaviors();
            var experiencesCount = userProfileProgress.getExperiencesCount();
            var capacitiesCount = userProfileProgress.getCapacitiesCount();

            if (capacitiesCount >= 10 && experiencesCount >= 1) {
                return "Correct";
            } else if (capacitiesCount < 10 || experiencesCount == 0 || !hasBehaviors) {
                return "Faible";
            } else {
                return "Méconnu";
            }
        });
    }

    private String formatTimestamp(Long createdTimestampMs) {
        return createdTimestampMs == null ? "" : StringUtils.formatTimestamp(createdTimestampMs);
    }

    private String formatTimestamp(LocalDateTime offsetDateTime) {
        return offsetDateTime == null ? "(Non défini)" : StringUtils.formatTimestamp(offsetDateTime);
    }

    @Transactional(readOnly = true)
    @RolesAllowed(Role.ODAS_ADMIN)
    public void writeUsersByGroupCsv(OutputStreamWriter writer, String organizationCode, boolean deanonymizedUser) {
        var roles = keycloakService.getRolesForGroup(organizationCode);
        var organizationTitleForCode = recruiterRepository.findByCodeIn(roles)
                .stream()
                .collect(Collectors.toMap(Recruiter::getCode, Recruiter::getTitle));

        var usersProfileProgress = userProfileRepository.getUserProfilesProgressByChannels(roles, securityService.isAdmin(), Pageable.unpaged(), null);

        @SuppressWarnings("java:S3958")
        var usersStream = usersProfileProgress.map(user ->
                buildUserExportLine(
                        false,
                        null,
                        keycloakService.getFrontOfficeUserProfile(user.getUserId()).orElseGet(() -> KeycloakUtils.defaultUserRepresentation(user.getUserId())),
                        Optional.of(user),
                        organizationTitleForCode,
                        deanonymizedUser)
        );

        try (var csvWriter = new CSVWriter(
                writer,
                StringUtils.CSV_FIELD_SEPARATOR,
                DEFAULT_QUOTE_CHARACTER,
                DEFAULT_ESCAPE_CHARACTER,
                DEFAULT_LINE_END)) {
            csvWriter.writeNext(buildExportHeaders(false, false, false, deanonymizedUser));
            csvWriter.writeAll(usersStream);
        } catch (IOException e) {
            log.error("Unable to generate CSV", e);
        }
    }

    private String[] buildHandicapExportHeaders() {
        return new String[]{"Identifiant", "Date de création", "Prescripteur", "Source", "jenesuispasunhandicap (origine) O/N", "jenesuispasunhandicap (actif) O/N"};
    }

    private String[] buildHandicapUserExportLine(UserRepresentation userRepresentation, Map<String, String> organizationTitleForCode) {
        var userProfile = userProfileRepository.findByUserId(userRepresentation.getId());

        // Prescripteur information
        var prescriberCode = userProfile.map(up -> up.prescriber().channel());
        var prescriberName = prescriberCode.map(organizationTitleForCode::get).orElse("Inconnu");
        var prescriberLabel = prescriberCode.map(code -> "%s (%s)".formatted(prescriberName, code)).orElse(NONE);

        // Source information
        var source = userProfile.map(UserProfile::channelSourceType)
                .map(UserChannel.ChannelSourceType::text)
                .orElse(UserChannel.ChannelSourceType.UNKNOWN.text());

        // Handicap flags
        var isFromHandicap = userProfile.map(UserProfile::getIsFromHandicap).orElse(false) ? "O" : "N";
        var handicapModeEnabled = userProfile.map(UserProfile::getHandicapModeEnabled).orElse(false) ? "O" : "N";

        return new String[]{
                userRepresentation.getId(),
                formatTimestamp(userRepresentation.getCreatedTimestamp()),
                prescriberLabel,
                source,
                isFromHandicap,
                handicapModeEnabled
        };
    }

    private Location buildLocationFromDTO(LocationDTO dto) {
        return Location.builder()
                .city(dto.getCity())
                .citycode(dto.getCitycode())
                .postcode(dto.getPostcode())
                .departmentCode(dto.getDepartmentCode())
                .regionName(dto.getRegionName())
                .longitude(dto.getLongitude())
                .latitude(dto.getLatitude())
                .radiusInKm(dto.getRadiusInKm())
                .build();
    }

}
