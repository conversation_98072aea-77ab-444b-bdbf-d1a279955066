package com.erhgo.controller;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.classifications.erhgooccupation.MasteryLevel;
import com.erhgo.domain.enums.RecruitmentState;
import com.erhgo.domain.job.Job;
import com.erhgo.domain.job.RecruitmentMotherObject;
import com.erhgo.domain.job.SpontaneousCandidatureMotherObject;
import com.erhgo.domain.recruitment.Recruitment;
import com.erhgo.domain.referential.AbstractOrganization;
import com.erhgo.domain.referential.Capacity;
import com.erhgo.domain.referential.Recruiter;
import com.erhgo.domain.userprofile.Location;
import com.erhgo.domain.userprofile.UserProfile;
import com.erhgo.domain.userprofile.UserProfileMotherObject;
import com.erhgo.generators.*;
import com.erhgo.security.Role;
import com.erhgo.security.WithMockKeycloakUser;
import com.erhgo.services.mailing.RecruitmentNotificationGenerator;
import com.erhgo.utils.DateTimeUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.IntStream;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

class StatisticsControllerTest extends AbstractIntegrationTest {
    @Autowired
    private CandidatureGenerator candidatureGenerator;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private CapacityGenerator capacityGenerator;
    @Autowired
    private RecruitmentNotificationGenerator recruitmentNotificationGenerator;
    @Autowired
    private JobGenerator jobGenerator;
    private final Date dateReference = new GregorianCalendar(2024, Calendar.JULY, 15).getTime();

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_monthly_candidatures_stats() {

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, matchingCapacity);
        var recruiter = createRecruiter();

        // Créer des recrutements pour Rhône-Alpes pour le mois de juin 2024
        var rhoneAlpesRecruitment1 = createRecruitment("69000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);
        var rhoneAlpesRecruitment2 = createRecruitment("42000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);
        var rhoneAlpesRecruitment3 = createRecruitment("01000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);
        var rhoneAlpesRecruitment4 = createRecruitment("26000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);
        // --- Recrutement sans candidature
        createRecruitment("26000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);
        // Recrutement actif sur plusieurs mois (mois de juin, mai et avril 2024)
        var rhoneAlpesRecruitmentMultiMonth1 = createRecruitment("69000", addMonthsToDate(-3), addMonthsToDate(-1), recruiter, job);

        // --- Recrutement DRAFT qui ne devrait pas être compté
        createDraftRecruitment("69100", addMonthsToDate(-2), recruiter, job);
        createDraftRecruitment("69008", addMonthsToDate(-2), recruiter, job);
        createDraftRecruitment("69003", addMonthsToDate(-2), recruiter, job);
        // Créer des recrutements pour Rhône-Alpes pour le mois de mai 2024
        var rhoneAlpesRecruitmentOtherMonth1 = createRecruitment("38000", addMonthsToDate(-2), addMonthsToDate(-2), recruiter, job);
        var rhoneAlpesRecruitmentOtherMonth2 = createRecruitment("63000", addMonthsToDate(-2), addMonthsToDate(-2), recruiter, job);
        var rhoneAlpesRecruitmentOtherMonth3 = createRecruitment("43000", addMonthsToDate(-2), addMonthsToDate(-2), recruiter, job);
        var rhoneAlpesRecruitmentOtherMonth4 = createRecruitment("07000", addMonthsToDate(-2), addMonthsToDate(-2), recruiter, job);

        // Créer des recrutements hors Rhône-Alpes pour le mois de mai 2024
        var notRhoneAlpesRecruitment1 = createRecruitment("75000", addMonthsToDate(-2), addMonthsToDate(-2), recruiter, job);
        var notRhoneAlpesRecruitment2 = createRecruitment("13000", addMonthsToDate(-2), addMonthsToDate(-2), recruiter, job);
        var notRhoneAlpesRecruitment3 = createRecruitment("59000", addMonthsToDate(-2), addMonthsToDate(-2), recruiter, job);

        // Recrutement actif sur plusieurs mois (mois de mai et avril 2024)
        var notRhoneAlpesRecruitmentMultiMonth1 = createRecruitment("75000", addMonthsToDate(-3), addMonthsToDate(-2), recruiter, job);
        // Créer des recrutements hors Rhône-Alpes pour le mois d'avril 2024
        var notRhoneAlpesRecruitmentOtherMonth1 = createRecruitment("34000", addMonthsToDate(-3), addMonthsToDate(-3), recruiter, job);
        var notRhoneAlpesRecruitmentOtherMonth2 = createRecruitment("59000", addMonthsToDate(-3), addMonthsToDate(-3), recruiter, job);
        var notRhoneAlpesRecruitmentOtherMonth3 = createRecruitment("13000", addMonthsToDate(-3), addMonthsToDate(-3), recruiter, job);

        // Créer des utilisateurs avec des emails différents
        var user1 = createUser("<EMAIL>", matchingCapacity);
        var user2 = createUser("<EMAIL>", matchingCapacity);
        var user3 = createUser("<EMAIL>", matchingCapacity);
        var user4 = createUser("<EMAIL>", matchingCapacity);
        var user5 = createUser("<EMAIL>", matchingCapacity);
        var user6 = createUser("<EMAIL>", matchingCapacity);
        var user7 = createUser("<EMAIL>", matchingCapacity);

        // Créer des candidatures pour Rhône-Alpes (mois de juin 2024, 20 candidatures prises en compte et 7 en DRAFT non prises en compte pour 5 recrutements pris en compte, 3 recrutements DRAFT non pris en compte)
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitment1, addMonthsToDate(-1), user1, user2, user3, user4, user5, user6, user7);
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitment2, addMonthsToDate(-1), user1, user2, user3, user4, user5);
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitment3, addMonthsToDate(-1), user1, user2, user3, user4, user5, user6, user7);
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitment4, addMonthsToDate(-1), user1);
        // Candidatures pour le recrutement actif sur plusieurs mois (4 candidatures en juin 2024) ce qui un total de 24 candidatures pour 5 recrutements au mois de juin 2024
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitmentMultiMonth1, addMonthsToDate(-1), user1, user2, user3, user4);
        // --- Candidatures DRAFT qui ne devraient pas être comptées
        createCandidatureNotFinalized(rhoneAlpesRecruitment2, user6, user7);
        createCandidatureNotFinalized(rhoneAlpesRecruitment4, user2, user3, user4, user5, user6);

        // Créer des candidatures pour Rhône-Alpes hors (mois de mai 2024, 12 candidatures pour 4 recrutements)
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitmentOtherMonth1, addMonthsToDate(-2), user1, user2, user3, user4, user5, user6, user7);
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitmentOtherMonth2, addMonthsToDate(-2), user1, user2, user3);
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitmentOtherMonth3, addMonthsToDate(-2), user1);
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitmentOtherMonth4, addMonthsToDate(-2), user1);

        // Créer des candidatures hors Rhône-Alpes (mois de mai 2024, 12 candidatures pour 3 recrutements)
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitment1, addMonthsToDate(-2), user1, user2, user3, user4, user5, user6, user7);
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitment2, addMonthsToDate(-2), user1, user2, user3);
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitment3, addMonthsToDate(-2), user1, user2);
        // Candidatures pour le recrutement actif sur plusieurs mois (3 candidatures en mai 2024) ce qui un total de 15 candidatures pour 4 recrutements au mois de mai 2024
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitmentMultiMonth1, addMonthsToDate(-2), user4, user5, user6);

        // Créer des candidatures hors Rhône-Alpes hors (mois d'avril 2024, 18 candidatures pour 3 recrutements) + 1 multirecrutement actif sur plusieurs mois
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitmentOtherMonth1, addMonthsToDate(-3), user1, user2, user3, user4, user5, user6, user7);
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitmentOtherMonth2, addMonthsToDate(-3), user1, user2, user3, user4, user5, user6, user7);
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitmentOtherMonth3, addMonthsToDate(-3), user1, user2, user3, user4);

        performGetAndExpect("/statistics/candidatures", "monthlyCandidaturesStats", false);
    }

    private Date addMonthsToDate(int months) {
        return DateUtils.addMonths(dateReference, months);
    }

    private Recruiter createRecruiter() {
        return applicationContext.getBean(OrganizationGenerator.class)
                .createRecruiter("C-42", "Le recruteur", AbstractOrganization.OrganizationType.SOURCING);
    }

    private Recruitment createRecruitment(String postcode, Date publicationDate, Date endDate, Recruiter recruiter, Job job) {
        return applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.PUBLISHED)
                .withLocation(Location.builder().postcode(postcode).build())
                .withPublicationDate(publicationDate)
                .withPublicationEndDate(DateTimeUtils.dateToOffsetDate(endDate))
                .withRecruiter(recruiter)
                .withJob(job)
                .buildAndPersist();
    }

    private void createDraftRecruitment(String postcode, Date publicationDate, Recruiter recruiter, Job job) {
        var publicationEndDate = OffsetDateTime.ofInstant(publicationDate.toInstant(), ZoneId.systemDefault()).plusDays(28);
        applicationContext.getBean(RecruitmentMotherObject.class)
                .withState(RecruitmentState.DRAFT)
                .withLocation(Location.builder().postcode(postcode).build())
                .withPublicationDate(publicationDate)
                .withPublicationEndDate(publicationEndDate)
                .withRecruiter(recruiter)
                .withJob(job)
                .buildAndPersist();
    }

    private UserProfile createUser(String email, Capacity matchingCapacity) {
        return applicationContext.getBean(UserProfileMotherObject.class)
                .withUserId(UUID.randomUUID().toString())
                .withFirstname("User")
                .withEmail(email)
                .buildAndPersist();
    }


    private void createCandidaturesWithSubmissionDate(Recruitment recruitment, Date submissionDate, UserProfile... users) {
        for (UserProfile user : users) {
            candidatureGenerator.createCandidatureWithSubmissionDate(user, recruitment, DateTimeUtils.dateToOffsetDate(submissionDate));
        }
    }

    private void createCandidatureNotFinalized(Recruitment recruitment, UserProfile... users) {
        for (UserProfile user : users) {
            candidatureGenerator.createCandidatureNotFinalized(user, recruitment);
        }
    }


    private void createRecruitmentNotification(Recruitment recruitment, UserProfile... users) {
        for (UserProfile user : users) {
            recruitmentNotificationGenerator.generateNotifications(Set.of(user.userId()), recruitment.getId());
        }
    }

    private void createSpontaneousCandidatureWithSubmissionDate(Date submissionDate) {
        applicationContext.getBean(SpontaneousCandidatureMotherObject.class)
                .withUserProfile(applicationContext.getBean(UserProfileMotherObject.class).withUserId(UUID.randomUUID().toString()).withFirstname("User").buildAndPersist())
                .withRecruiter(applicationContext.getBean(RecruiterMotherObject.class).withTitle("Organisation A").withOrganizationType(AbstractOrganization.OrganizationType.SOURCING).withCode("S-A").buildAndPersist())
                .withSubmissionDate(DateTimeUtils.dateToOffsetDate(submissionDate))
                .buildAndPersist();
    }


    private void createUsersWithModifiedAttitude(int count) {
        IntStream.range(0, count)
                .forEach(i -> applicationContext.getBean(UserProfileMotherObject.class)
                        .withUserId(UUID.randomUUID().toString())
                        .withBehaviorDescriptionModifiedDate(OffsetDateTime.now())
                        .buildAndPersist());
    }

    private void createUsersWithoutModifiedAttitude(int count) {
        IntStream.range(0, count)
                .forEach(i -> applicationContext.getBean(UserProfileMotherObject.class)
                        .withUserId(UUID.randomUUID().toString())
                        .buildAndPersist());
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_monthly_candidatures_on_multiple_months() {

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, matchingCapacity);
        var recruiter = createRecruiter();

        // Recrutement actif sur plusieurs mois
        var rhoneAlpesRecruitmentMultiMonth1 = createRecruitment("69000", addMonthsToDate(-2), addMonthsToDate(0), recruiter, job);
        createRecruitment("69000", addMonthsToDate(-1), addMonthsToDate(1), recruiter, job);
        createRecruitment("69000", addMonthsToDate(0), addMonthsToDate(1), recruiter, job);

        // Créer des candidatures pour ce recrutement dans différents mois
        var user1 = createUser("<EMAIL>", matchingCapacity);
        var user2 = createUser("<EMAIL>", matchingCapacity);
        var user3 = createUser("<EMAIL>", matchingCapacity);
        var user4 = createUser("<EMAIL>", matchingCapacity);

        // Candidatures en mai 2024
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitmentMultiMonth1, addMonthsToDate(-2), user1, user2);
        // Candidatures en juin 2024
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitmentMultiMonth1, addMonthsToDate(-1), user3, user4);

        performGetAndExpect("/statistics/candidatures", "monthlyCandidaturesStatsMultipleMonths", false);
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_monthly_candidatures_stats_with_notifications() {

        var matchingCapacity = capacityGenerator.createCapacity("CA-1");
        var job = jobGenerator.createJobWithCapacitiesAndLevel("J-1", MasteryLevel.MIN_LEVEL, matchingCapacity);
        var recruiter = createRecruiter();

        // Créer des recrutements pour Rhône-Alpes pour le mois de juin 2024
        var rhoneAlpesRecruitment1 = createRecruitment("69000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);
        var rhoneAlpesRecruitment2 = createRecruitment("42000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);
        var rhoneAlpesRecruitment3 = createRecruitment("01000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);

        // Créer des recrutements hors Rhône-Alpes pour le mois de juin 2024
        var notRhoneAlpesRecruitment1 = createRecruitment("75000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);
        var notRhoneAlpesRecruitment2 = createRecruitment("13000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);
        var notRhoneAlpesRecruitment3 = createRecruitment("59000", addMonthsToDate(-1), addMonthsToDate(-1), recruiter, job);

        // Créer des utilisateurs
        var user1 = createUser("<EMAIL>", matchingCapacity);
        var user2 = createUser("<EMAIL>", matchingCapacity);
        var user3 = createUser("<EMAIL>", matchingCapacity);
        var user4 = createUser("<EMAIL>", matchingCapacity);
        var user5 = createUser("<EMAIL>", matchingCapacity);
        var user6 = createUser("<EMAIL>", matchingCapacity);
        var user7 = createUser("<EMAIL>", matchingCapacity);


        // Créer des candidatures pour Rhône-Alpes pour des recrutements classics
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitment1, addMonthsToDate(-1), user1, user2, user6); // 3 candidatures
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitment2, addMonthsToDate(-1), user3, user7); // 2 candidatures
        createCandidaturesWithSubmissionDate(rhoneAlpesRecruitment3, addMonthsToDate(-1), user4, user5, user1); // 3 candidatures
        // Créer des candidatures pour Rhône-Alpes pour des recrutements avec au moins une notification
        createRecruitmentNotification(rhoneAlpesRecruitment1, user1, user2, user3, user4); // 4 notif, 2 sur candidats
        createRecruitmentNotification(rhoneAlpesRecruitment2, user1, user2); // 2 notif, 0 sur candidat


        // Créer des candidatures hors Rhône-Alpes
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitment1, addMonthsToDate(-1), user4, user5, user2); // 3 candidatures
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitment2, addMonthsToDate(-1), user6, user3); // 2 candidatures
        createCandidaturesWithSubmissionDate(notRhoneAlpesRecruitment3, addMonthsToDate(-1), user1, user4); // 2 candidatures
        // Créer des candidatures hors Rhône-Alpes pour des recrutements avec au moins une notification
        createRecruitmentNotification(notRhoneAlpesRecruitment1, user1, user2, user3); // 3 notif, 1 sur candidat

        performGetAndExpect("/statistics/candidatures", "monthlyCandidaturesStatsWithNotifications", false);
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_monthly_spontaneous_candidatures_stats() {
        //3 candidatures spontanées en juillet 2024
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(0));
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(0));
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(0));
        //2 candidatures spontanées en juin 2024
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(-1));
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(-1));
        // 5 candidatures spontanées en mai 2024
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(-2));
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(-2));
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(-2));
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(-2));
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(-2));
        // 1 candidature spontanée en août 2024
        createSpontaneousCandidatureWithSubmissionDate(addMonthsToDate(1));
        performGetAndExpect("/statistics/spontaneous-candidatures", "monthlySpontaneousCandidaturesStats", false);
    }


    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_user_profiles_with_modified_attitude_in_several_metrics_stats() throws Exception {
        createUsersWithModifiedAttitude(11);
        createUsersWithoutModifiedAttitude(3);

        performGetAndExpect("/statistics/several-metrics", "severalMetricsStatsWithModifiedAttitude", false)
                .andExpect(jsonPath("$.userProfilesWithModifiedAttitudeCount").value(11));

    }

    @Test
    @ResetDataAfter
    @WithMockKeycloakUser(roles = {Role.ODAS_ADMIN})
    void get_handicap_metrics_in_several_metrics_stats() throws Exception {
        createUsersWithModifiedAttitude(11);
        createUsersWithHandicapFlags(5, 3);
        createCandidaturesFromHandicapUsers(8);

        performGetAndExpect("/statistics/several-metrics", "severalMetricsStatsWithHandicapMetrics", false)
                .andExpect(jsonPath("$.userProfilesWithModifiedAttitudeCount").value(11))
                .andExpect(jsonPath("$.handicapOriginAccountsCount").value(5))
                .andExpect(jsonPath("$.handicapActiveAccountsCount").value(3))
                .andExpect(jsonPath("$.candidaturesFromHandicapAccountsCount").value(8));
    }

    private void createUsersWithHandicapFlags(int originCount, int activeCount) {
        // Create users with isFromHandicap = true
        for (int i = 0; i < originCount; i++) {
            var userProfile = context.getBean(UserProfileMotherObject.class).buildAndPersist();
            userProfile.isFromHandicap(true);
            userProfileRepository.save(userProfile);
        }

        // Create users with handicapModeEnabled = true
        for (int i = 0; i < activeCount; i++) {
            var userProfile = context.getBean(UserProfileMotherObject.class).buildAndPersist();
            userProfile.handicapModeEnabled(true);
            userProfileRepository.save(userProfile);
        }
    }

    private void createCandidaturesFromHandicapUsers(int count) {
        // Create users with handicapModeEnabled = true and candidatures
        for (int i = 0; i < count; i++) {
            var userProfile = context.getBean(UserProfileMotherObject.class).buildAndPersist();
            userProfile.handicapModeEnabled(true);
            userProfileRepository.save(userProfile);

            // Create a candidature for this user
            var candidature = context.getBean(RecruitmentCandidatureMotherObject.class)
                    .withUserProfile(userProfile)
                    .buildAndPersist();
        }
    }


}
