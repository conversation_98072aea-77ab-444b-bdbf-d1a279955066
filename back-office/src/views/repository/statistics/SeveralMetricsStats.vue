<template>
  <div>

  <v-card>
    <v-card-title primary-title>
      <h3>
        <v-icon large color="primary">mdi-comment-plus-outline</v-icon>
        {{ $t('statistics.severalMetricsStats') }}
        <v-icon medium color="green" @click="showInfoDialog = true">mdi-information</v-icon>
      </h3>
    </v-card-title>

    <v-card-text>
      <v-alert
        v-if="loading"
        type="info"
        prominent
      >
        <v-icon left size="30">mdi-loading mdi-spin</v-icon>
        Loading...
      </v-alert>
      <v-alert
        v-else-if="severalMetricsStats?.userProfilesWithModifiedAttitudeCount !== undefined"
        type="info"
        prominent
      >
        <v-icon left size="30">mdi-file-document-edit-outline</v-icon>
        Utilisateurs ayant modifié leurs attitudes :
        <span class="text-h6 font-weight-bold">{{ severalMetricsStats.userProfilesWithModifiedAttitudeCount }}</span>
      </v-alert>

      <v-alert
        v-if="severalMetricsStats?.handicapOriginAccountsCount !== undefined"
        type="success"
        prominent
      >
        <v-icon left size="30">mdi-account-heart</v-icon>
        Comptes flagués handicap (origine) :
        <span class="text-h6 font-weight-bold">{{ severalMetricsStats.handicapOriginAccountsCount }}</span>
      </v-alert>

      <v-alert
        v-if="severalMetricsStats?.handicapActiveAccountsCount !== undefined"
        type="success"
        prominent
      >
        <v-icon left size="30">mdi-account-check</v-icon>
        Comptes handicap (actif) :
        <span class="text-h6 font-weight-bold">{{ severalMetricsStats.handicapActiveAccountsCount }}</span>
      </v-alert>

      <v-alert
        v-if="severalMetricsStats?.candidaturesFromHandicapAccountsCount !== undefined"
        type="warning"
        prominent
      >
        <v-icon left size="30">mdi-briefcase-account</v-icon>
        Candidatures des comptes handicap actifs :
        <span class="text-h6 font-weight-bold">{{ severalMetricsStats.candidaturesFromHandicapAccountsCount }}</span>
      </v-alert>
    </v-card-text>

  </v-card>
    <v-dialog v-model="showInfoDialog" max-width="60%">
      <v-card>
        <v-card-title>
          <span class="text-h5">Informations sur les calculs</span>
        </v-card-title>
        <v-card-text>
          <p>Ces indicateurs nous donnent des informations sur les utilisateurs et leurs interactions avec la plateforme&nbsp;:</p>
          <ul>
            <li>
              <strong>Nombre de personnes ayant modifié leur attitude&nbsp;:</strong> Nombre de personnes ayant modifié l'attitude générée par LLM (typiquement&nbsp;: sur le FO depuis la liste des expériences).
            </li>
            <li>
              <strong>Comptes flagués handicap (origine)&nbsp;:</strong> Nombre de comptes créés via le parcours handicap (isFromHandicap = true).
            </li>
            <li>
              <strong>Comptes handicap (actif)&nbsp;:</strong> Nombre de comptes ayant le mode handicap activé (handicapModeEnabled = true).
            </li>
            <li>
              <strong>Candidatures des comptes handicap actifs&nbsp;:</strong> Nombre total de candidatures soumises par les utilisateurs ayant le mode handicap activé.
            </li>
          </ul>
        </v-card-text>
        <v-card-actions>
          <v-spacer/>
          <v-btn color="primary" @click="showInfoDialog = false">Fermer</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import StatisticsService from './StatisticsService';

export default {
  name: 'SeveralMetricsStats',
  async created() {
    this.statisticsService = new StatisticsService();
    this.loading = true;
    await this.statisticsService.fetchSeveralMetricsStats();
    this.loading = false;
  },
  data() {
    return {
      statisticsService: null,
      showInfoDialog: false,
      loading: false,
    };
  },
  computed: {
    severalMetricsStats() {
      return this.statisticsService?.severalMetricsStats;
    },
  },
};
</script>

<style lang="scss">
.spontaneous-candidatures-stats {
  .stats tr:nth-child(even) {
    background-color: #ECEFF1;
  }
}
</style>
