/public/version:
  get:
    $ref: "./public/paths/GetVersion.yaml"
/public/recruitment/{recruitmentCode}:
  get:
    $ref: "./public/paths/GetPublicRecruitmentInfo.yaml"
/public/log:
  post:
    $ref: "./public/paths/LogEvent.yaml"
/public/trimoji:
  post:
    $ref: "./public/paths/PostTrimoji.yaml"
/public/landing-page/{urlKey}:
  get:
    $ref: "./landing-page/paths/GetLandingPageContent.yaml"


/external-offer/simulate-ats-offer:
  post:
    $ref: "./external-offer/paths/SimulateAtsOffer.yaml"
/external-offer/initialize-extracted-offer-data:
  get:
    $ref: "./external-offer/paths/InitializeExtractedDataOffer.yaml"
/external-offer/organization/list:
  get:
    $ref: "./external-offer/paths/GetExternalOffers.yaml"
/external-offer/{uuid}/integrate:
  put:
    $ref: "./external-offer/paths/IntegrateExternalOffer.yaml"
/external-offer/{uuid}/ignore:
  put:
    $ref: "./external-offer/paths/IgnoreExternalOffer.yaml"
/external-offer/scrape-offers:
  post:
    $ref: "./external-offer/paths/ScrapeOffers.yaml"


/trimoji:
  get:
    operationId: getTrimojiUrl
    responses:
      200:
        description: Trimoji url
        content:
          application/json:
            schema:
              type: object
              title: TrimojiUrl
              required:
                - trimojiUrl
              properties:
                trimojiUrl:
                  type: string
  put:
    operationId: markEndOfTest
    responses:
      204:
        description: Test is correctly marked as finished for authenticated user


/config/algolia-search:
  get:
    $ref: "./config/paths/GetAlgoliaSearchConfig.yaml"
/config/configurable-properties:
  get:
    $ref: "./config/paths/GetAllConfigurableProperties.yaml"
/config/edit-configurable-property:
  post:
    $ref: "./config/paths/EditConfigurableProperty.yaml"


/recruitment/create:
  post:
    $ref: "./recruitment/paths/SaveRecruitment.yaml"
/recruitment/{recruitmentId}/update:
  patch:
    $ref: "./recruitment/paths/UpdateRecruitment.yaml"
/recruitment/{recruitmentId}/diffusion-type:
  put:
    $ref: "./recruitment/paths/UpdateRecruitmentDiffusionType.yaml"
/recruitment/{recruitmentId}/refreshMatching:
  post:
    $ref: "./recruitment/paths/RefreshMatching.yaml"
/recruitment/candidature/{candidatureId}/meet:
  post:
    $ref: "./recruitment/paths/MeetCandidature.yaml"
/recruitment/matching-candidature/{candidatureId}:
  get:
    $ref: "./recruitment/paths/GetMatchingCandidature.yaml"
/recruitment/{recruitmentId}/matching/candidatures/selected:
  get:
    $ref: "./recruitment/paths/GetCandidaturesForConsultant.yaml"
/recruitment/{recruitmentId}/matching:
  get:
    $ref: "./recruitment/paths/GetRecruitmentForMatching.yaml"
# Do not delete: used by URL in back-office/src/views/setup/recruitment/Edit.vue to fetch recruitment
/recruitment/{recruitmentCode}:
  get:
    $ref: "./recruitment/paths/GetRecruitmentForUpdate.yaml"
/recruitment/{recruitmentCode}/change-state:
  post:
    $ref: "./recruitment/paths/ChangeRecruitmentState.yaml"
/recruitment/list:
  get:
    $ref: "./recruitment/paths/ListRecruitments.yaml"
/recruitment/{jobId}/candidate/{userId}/list:
  get:
    $ref: "./recruitment/paths/ListRecruitmentsForCandidate.yaml"
/recruitment/published-for-job/{jobId}:
  get:
    $ref: "./recruitment/paths/ListRecruitmentsForJob.yaml"
/recruitment/{recruitmentId}/send-candidature-proposal:
  post:
    $ref: "./recruitment/paths/SendCandidatureProposal.yaml"
/recruitment/{userId}/list-user-recruitment-reports:
  get:
    $ref: "./recruitment/paths/ListUserRecruitmentReports.yaml"
/recruitment/csv-import:
  post:
    $ref: "./recruitment/paths/ImportCsvRecruitments.yaml"


/context/create:
  post:
    $ref: "./referential/context/paths/CreateNewContext.yaml"
/context/{contextId}/update:
  post:
    $ref: "./referential/context/paths/UpdateContext.yaml"
/context/{contextId}:
  get:
    $ref: "./referential/context/paths/GetContext.yaml"
/context/list:
  get:
    $ref: "./referential/context/paths/ListContexts.yaml"
/context/search:
  get:
    $ref: "./referential/context/paths/SearchContexts.yaml"


/questionContext/save:
  put:
    $ref: "./referential/context/paths/SaveQuestionForContexts.yaml"
/questionContext/{uuid}:
  get:
    $ref: "./referential/context/paths/GetQuestionForContexts.yaml"
/questionContext/list/{contextId}:
  get:
    $ref: "./referential/context/paths/GetQuestionForContextsByContextId.yaml"
/questionContext/list:
  get:
    $ref: "./referential/context/paths/ListQuestionForContexts.yaml"
/questionContext/{questionId}/context/recruitmentProfiles/{contextId}:
  get:
    $ref: "./referential/context/paths/GetRecruitmentProfilesByContextQuestionAndContextId.yaml"


/capacity-related-question/question/save:
  put:
    $ref: "./referential/capacity-related-question/paths/SaveQuestion.yaml"
/capacity-related-question/question/{questionId}:
  get:
    $ref: "./referential/capacity-related-question/paths/GetQuestion.yaml"
/capacity-related-question/question/list:
  get:
    $ref: "./referential/capacity-related-question/paths/GetQuestionPage.yaml"
/capacity-related-question/question/reorderQuestions:
  post:
    $ref: "./referential/capacity-related-question/paths/ReorderQuestions.yaml"
/capacity-related-question/question/for-user:
  get:
    $ref: "./user/capacity-related-question/paths/GetQuestionsForUser.yaml"
  put:
    $ref: "./user/capacity-related-question/paths/SaveAnswerForUser.yaml"
/capacity-related-question/{userId}/sumup:
  get:
    $ref: "./user/capacity-related-question/paths/GetQuestionsSumupForUser.yaml"

/esco/occupations:
  get:
    $ref: "./classifications/esco/paths/EscoOccupationPage.yaml"
/esco/occupation/capacities:
  get:
    $ref: "./classifications/esco/paths/GetEscoOccupationCapacities.yaml"
/esco/occupation/detail:
  get:
    $ref: "./classifications/esco/paths/GetEscoOccupation.yaml"
/esco/occupation/list:
  get:
    $ref: "./classifications/esco/paths/SearchEscoOccupation.yaml"
/esco/skill/detail:
  get:
    $ref: "./classifications/esco/paths/GetSkill.yaml"
/esco/skill/linkToActivity:
  post:
    $ref: "./classifications/esco/paths/LinkToActivity.yaml"
  delete:
    $ref: "./classifications/esco/paths/DeleteLinkToActivity.yaml"
/esco/skill/linkToBehavior:
  post:
    $ref: "./classifications/esco/paths/LinkToBehavior.yaml"
  delete:
    $ref: "./classifications/esco/paths/DeleteLinkToBehavior.yaml"
/esco/skill/linkToContext:
  post:
    $ref: "./classifications/esco/paths/LinkToContext.yaml"
  delete:
    $ref: "./classifications/esco/paths/DeleteLinkToContext.yaml"
/esco/skill/detail/setNoActivity:
  post:
    $ref: "./classifications/esco/paths/SetSkillNoActivity.yaml"
/esco/skill/detail/setNoBehavior:
  post:
    $ref: "./classifications/esco/paths/SetSkillNoBehavior.yaml"
/esco/skill/detail/setNoContext:
  post:
    $ref: "./classifications/esco/paths/SetSkillNoContext.yaml"
/esco/skill/detail/updateDescription:
  post:
    $ref: "./classifications/esco/paths/UpdateSkillDescription.yaml"

/rome/list:
  get:
    $ref: "./classifications/rome/paths/RomePage.yaml"


/erhgo-occupation:
  put:
    $ref: "./classifications/erhgo-occupation/paths/CreateErhgoOccupation.yaml"
/erhgo-occupation/reindex:
  post:
    $ref: "./classifications/erhgo-occupation/paths/ReindexErhgoOccupations.yaml"
/erhgo-occupation/qualify:
  put:
    $ref: "./classifications/erhgo-occupation/paths/QualifyOccupationState.yaml"
/erhgo-occupation/unqualify:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UnqualifyOccupationState.yaml"
/erhgo-occupation/update-mastery-level:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UpdateMasteryLevel.yaml"
/erhgo-occupation/update-behaviors-description:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UpdateBehaviorsDescription.yaml"
/erhgo-occupation/update-erhgo-classifications:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UpdateErhgoClassifications.yaml"
/erhgo-occupation/update-work-environments:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UpdateWorkEnvironments.yaml"
/erhgo-occupation/update-specifications:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UpdateSpecifications.yaml"
/erhgo-occupation/detail/{id}:
  get:
    $ref: "./classifications/erhgo-occupation/paths/GetErhgoOccupation.yaml"
/erhgo-occupation/sum-up/{id}:
  get:
    $ref: "./classifications/erhgo-occupation/paths/GetErhgoOccupationSumUp.yaml"
/erhgo-occupation/list:
  get:
    $ref: "./classifications/erhgo-occupation/paths/GetErhgoOccupationPage.yaml"
/erhgo-occupation/simple-list:
  get:
    $ref: "./classifications/erhgo-occupation/paths/GetErhgoOccupationOTPage.yaml"
/erhgo-occupation/rome/linkToErhgoOccupation:
  put:
    $ref: "./classifications/erhgo-occupation/paths/LinkRomeToErhgoOccupation.yaml"
/erhgo-occupation/rome/unlinkFromErhgoOccupation:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UnlinkRomeFromErhgoOccupation.yaml"
/erhgo-occupation/esco/linkToErhgoOccupation:
  put:
    $ref: "./classifications/erhgo-occupation/paths/LinkEscoOccupationToErhgoOccupation.yaml"
/erhgo-occupation/esco/unlinkFromErhgoOccupation:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UnlinkEscoOccupationFromErhgoOccupation.yaml"
/erhgo-occupation/addActivities:
  put:
    $ref: "./classifications/erhgo-occupation/paths/AddActivitiesToOccupation.yaml"
/erhgo-occupation/removeActivities:
  put:
    $ref: "./classifications/erhgo-occupation/paths/RemoveActivitiesFromOccupation.yaml"
/erhgo-occupation/changeActivityOccupationState:
  put:
    $ref: "./classifications/erhgo-occupation/paths/ChangeActivityOccupationState.yaml"
/erhgo-occupation/addContext:
  put:
    $ref: "./classifications/erhgo-occupation/paths/AddContextToOccupation.yaml"
/erhgo-occupation/removeContext:
  put:
    $ref: "./classifications/erhgo-occupation/paths/RemoveContextFromOccupation.yaml"
/erhgo-occupation/changeContextOccupationState:
  put:
    $ref: "./classifications/erhgo-occupation/paths/ChangeContextOccupationState.yaml"
/erhgo-occupation/addBehavior:
  put:
    $ref: "./classifications/erhgo-occupation/paths/AddBehaviorToOccupation.yaml"
/erhgo-occupation/removeBehavior:
  put:
    $ref: "./classifications/erhgo-occupation/paths/RemoveBehaviorFromOccupation.yaml"
/erhgo-occupation/editAlternativeLabels:
  put:
    $ref: "./classifications/erhgo-occupation/paths/EditAlternativeLabels.yaml"
/erhgo-occupation/occupation/search-occupations:
  get:
    $ref: "./classifications/erhgo-occupation/paths/SearchOccupations.yaml"
/erhgo-occupation/skill/unlinkFromErhgoOccupation:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UnlinkSkillFromErhgoOccupation.yaml"
/erhgo-occupation/changeBehaviorCategory:
  put:
    $ref: "./classifications/erhgo-occupation/paths/ChangeOccupationBehaviorCategory.yaml"
/erhgo-occupation/{id}/capacities:
  get:
    $ref: "./classifications/erhgo-occupation/paths/GetErhgoOccupationCapacities.yaml"
/erhgo-occupation/{id}/pdf:
  get:
    $ref: "./classifications/erhgo-occupation/paths/GetErhgoOccupationDetailsPdf.yaml"
/erhgo-occupation/merge:
  put:
    $ref: "./classifications/erhgo-occupation/paths/MergeOccupations.yaml"
/erhgo-occupation/update-criteria-values:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UpdateCriteriaValues.yaml"
/erhgo-occupation/labels:
  get:
    $ref: "./classifications/erhgo-occupation/paths/GetErhgoOccupationsForLabels.yaml"
/erhgo-occupation/update-description:
  put:
    $ref: "./classifications/erhgo-occupation/paths/UpdateDescription.yaml"


/erhgo-occupation-generation/generate-description/{id}:
  get:
    $ref: "./classifications/erhgo-occupation/paths/GenerateOccupationDescription.yaml"
/erhgo-occupation-generation/generate-activities/{id}:
  put:
    $ref: "./classifications/erhgo-occupation/paths/GenerateOccupationActivities.yaml"
/erhgo-occupation-generation/generate-behaviors-description/{id}:
  get:
    $ref: "./classifications/erhgo-occupation/paths/GenerateOccupationBehaviorsDescription.yaml"
/erhgo-occupation-generation/generate-behaviors/{id}:
  put:
    $ref: "./classifications/erhgo-occupation/paths/GenerateOccupationBehaviors.yaml"
/erhgo-occupation-generation/generate-erhgo-classifications/{id}:
  put:
    $ref: "./classifications/erhgo-occupation/paths/GenerateOccupationClassification.yaml"
/erhgo-occupation-generation/generate-erhgo-classifications-romes/{id}:
  put:
    $ref: "./classifications/erhgo-occupation/paths/GenerateOccupationClassificationRome.yaml"
/erhgo-occupation-generation/generate-erhgo-specification-and-mastery-level/{id}:
  put:
    $ref: "./classifications/erhgo-occupation/paths/GenerateOccupationSpecificationAndMasteryLevel.yaml"
/erhgo-occupation-generation/generate-global-qualification:
  put:
    $ref: "./classifications/erhgo-occupation/paths/GenerateOccupationGlobalQualification.yaml"
/erhgo-occupation-generation/find-best-matching-occupation:
  get:
    $ref: "./classifications/erhgo-occupation/paths/FindBestMatchingOccupation.yaml"
/erhgo-occupation-generation/generic-prompt-tester:
  post:
    $ref: "./generation/paths/GenericOpenAiPromptTester.yaml"


/mission/create:
  post:
    $ref: "./job/mission/paths/CreateNewMission.yaml"
/mission/{missionId}/update:
  post:
    $ref: "./job/mission/paths/UpdateMission.yaml"
/mission/{missionId}:
  delete:
    $ref: "./job/mission/paths/DeleteMission.yaml"


/job:
  put:
    $ref: "./job/job/paths/SaveJob.yaml"
/job/list:
  get:
    $ref: "./job/job/paths/GetJobPage.yaml"
/job/{jobId}/criteria:
  put:
    $ref: "./job/job/paths/UpdateCriteriaForJob.yaml"
/job/{jobId}/working-time-type:
  put:
    $ref: "./job/job/paths/AddWorkingTimeTypeForJob.yaml"
/job/{jobId}/behavior:
  put:
    $ref: "./job/job/paths/SaveBehavior.yaml"
/job/{jobId}/publish:
  put:
    $ref: "./job/job/paths/Publish.yaml"
/job/published/{recruiterCode}:
  get:
    $ref: "./job/job/paths/GetJobsPublishedByRecruiterCode.yaml"
/job/{jobId}/reorderMissions:
  post:
    $ref: "./job/job/paths/ReorderMissions.yaml"
/job/for-template:
  post:
    $ref: "./job/job/paths/CreateJobForTemplate.yaml"
/job/{jobId}/detail:
  get:
    $ref: "./job/job/paths/GetJob.yaml"
/job/{jobId}/delete:
  delete:
    $ref: "./job/job/paths/DeleteJob.yaml"
/job/{jobId}/capacities:
  get:
    $ref: "./job/job/paths/GetJobCapacities.yaml"
/job/{jobId}/recommendation:
  put:
    $ref: "./job/job/paths/SetRecommendationForJob.yaml"
/job/{jobId}/recruitmentProfile/list:
  get:
    $ref: "./job/job/paths/ListRecruitmentProfiles.yaml"
/job/{jobId}/recruitmentProfile/save:
  post:
    $ref: "./job/job/paths/SaveRecruitmentProfile.yaml"
/job/{jobId}/recruitmentProfile/{profileId}/detail:
  get:
    $ref: "./job/job/paths/GetRecruitmentProfile.yaml"
/job/{jobId}/recruitmentProfile/{profileId}/capacities:
  get:
    $ref: "./job/job/paths/GetProfileCapacities.yaml"
/job/{jobId}/recruitmentProfile/{profileId}/deleteOptionals:
  post:
    $ref: "./job/job/paths/DeleteOptionals.yaml"
/job/{jobId}/recruitmentProfile/{profileId}/mission/{missionId}/endQualification:
  post:
    $ref: "./job/job/paths/EndQualification.yaml"
/job/{jobId}/recruitmentProfile/{profileId}/addOptionals:
  post:
    $ref: "./job/job/paths/AddOptionals.yaml"
/job/{jobId}/recruitmentProfile/{profileId}/setContextQuestion:
  post:
    $ref: "./job/job/paths/SetContextQuestionForProfile.yaml"
/job/{jobId}/matching-users:
  get:
    $ref: "./job/job/paths/GetUsersMatchingJob.yaml"
/job/{jobId}/matching-users/export:
  get:
    $ref: "./job/job/paths/GetUsersMatchingJobExport.yaml"
/job/{jobId}/candidates:
  get:
    $ref: "./job/job/paths/GetJobCandidates.yaml"


/activityLabel/search:
  get:
    $ref: "./referential/activity/paths/SearchActivities.yaml"
/activityLabel/JOB/{id}/activity:
  get:
    $ref: "./referential/activity/paths/GetActivity.yaml"
/activityLabel/{id}/isDeletable:
  get:
    $ref: "./referential/activity/paths/IsActivityLabelDeletable.yaml"
/activityLabel/{activityType}/list:
  get:
    $ref: "./referential/activity/paths/ListActivityLabels.yaml"
/activity/{activityType}/save:
  post:
    $ref: "./referential/activity/paths/SaveActivity.yaml"
/activity/merge:
  post:
    $ref: "./referential/activity/paths/MergeActivities.yaml"


/userExperience/saveExperience:
  post:
    $ref: "./user/experience/paths/SaveExperience.yaml"
/userExperience/{experienceId}:
  get:
    $ref: "./user/experience/paths/GetExperience.yaml"
  delete:
    $ref: "./user/experience/paths/DeleteExperience.yaml"

/user/create-fo:
  post:
    $ref: "./user/user/paths/CreateUserFO.yaml"
/user/create:
  post:
    $ref: "./user/user/paths/CreateUser.yaml"
/user/deactivate-user-sourcing:
  put:
    $ref: "./user/user/paths/DeactivateUserSourcing.yaml"
/user/{userId}/activate-user-sourcing:
  put:
    $ref: "./user/user/paths/ActivateUserSourcing.yaml"
/user/{userId}:
  delete:
    $ref: "./user/user/paths/DeleteUser.yaml"
/user/{userId}/candidatures:
  get:
    $ref: "./user/user/paths/GetUserCandidatures.yaml"
/user/{userId}/simple-candidatures:
  get:
    $ref: "./user/user/paths/GetSimpleUserCandidatures.yaml"
/user/{organizationCode}/list/:
  get:
    $ref: "./user/user/paths/GetMembersOfGroups.yaml"
/user/{userId}/experiences:
  get:
    $ref: "./user/user/paths/GetUserExperiences.yaml"
/user/{userId}/profileSummary:
  get:
    $ref: "./user/user/paths/GetUserProfile.yaml"
/user/{userId}/profileDetailWithCapacities:
  get:
    $ref: "./user/user/paths/GetUserProfileDetailWithCapacities.yaml"
/user/{userId}/channel-affectations:
  get:
    $ref: "./user/user/paths/GetUserChannelAffectations.yaml"
/user/{userId}/criterias:
  get:
    $ref: "./user/user/paths/GetUserCriterias.yaml"
/user/simple-criterias:
  get:
    $ref: "./user/user/paths/GetSimpleUserCriterias.yaml"
/user/criterias:
  post:
    $ref: "./user/user/paths/SetUserCriterias.yaml"
/user/criterias/reset:
  delete:
    $ref: "./user/user/paths/ResetUserCriterias.yaml"
/user/blacklisted-occupations:
  post:
    $ref: "./user/user/paths/SetUserBlackListedOccupations.yaml"
/user/remove-blacklisted-occupation:
  post:
    $ref: "./user/user/paths/RemoveUserBlackListedOccupations.yaml"
/user/{userId}/erhgo-classifications:
  get:
    $ref: "./user/user/paths/GetUserErhgoClassifications.yaml"
/user/erhgo-classification:
  post:
    $ref: "./user/user/paths/SetUserErhgoClassification.yaml"
/user/{userId}/contact-info:
  get:
    $ref: "./user/user/paths/GetUserContactInfo.yaml"
  post:
    $ref: "./user/user/paths/SetUserContactInfo.yaml"
/user/name:
  post:
    $ref: "./user/user/paths/SetUserName.yaml"
/user/city:
  post:
    $ref: "./user/user/paths/SetUserCity.yaml"
/user/salary:
  post:
    $ref: "./user/user/paths/SetUserSalary.yaml"
/user/situation:
  post:
    $ref: "./user/user/paths/SetUserSituation.yaml"
/user/occupation:
  post:
    $ref: "./user/user/paths/SetUserOccupation.yaml"
/user/phone-number:
  post:
    $ref: "./user/user/paths/SetUserPhoneNumber.yaml"
/user/should-be-contacted:
  post:
    operationId: legacyUpdateShouldBeContactedDoNotUse
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            title: deprecatedCommandForLegacyMobileVersion
            properties:
              shouldBeContacted:
                type: boolean
    responses:
      204:
        description: Nothing done - for legacy purpose only
/user/no-experience:
  post:
    $ref: "./user/user/paths/TagNoExperienceForUser.yaml"
/user/front-office/list:
  get:
    $ref: "./user/user/paths/SearchFrontOfficeUser.yaml"
/user/front-office/export:
  post:
    $ref: "./user/user/paths/ExportUsers.yaml"
/user/front-office/export-handicap:
  get:
    $ref: "./user/user/paths/ExportHandicapUsers.yaml"
/user/front-office/list-by-group:
  get:
    $ref: "./user/user/paths/SearchFrontOfficeUserByGroups.yaml"
/user/front-office/list-by-group/export:
  get:
    $ref: "./user/user/paths/GetFrontOfficeUserByGroupExport.yaml"
/user/front-office/reset-password:
  post:
    $ref: "./user/user/paths/SetFrontOfficeUserPassword.yaml"
/user/confirm-user-from-bo:
  post:
    $ref: "./user/user/paths/ConfirmFOUserFromBO.yaml"
/user/front-office/resend-initial-mail:
  post:
    $ref: "./user/user/paths/ResendInitialMail.yaml"
/user/back-office/list:
  get:
    $ref: "./user/user/paths/GetBackOfficeUsers.yaml"
/user/{userId}/matching-jobs/candidatures:
  get:
    $ref: "./user/user/paths/GetUserJobsCandidatures.yaml"
/user/recruitments:
  get:
    $ref: "./user/user/paths/GetRecruitments.yaml"
/user/recruitments-count:
  get:
    $ref: "./user/user/paths/GetRecruitmentsCount.yaml"
/user/{userId}/capacities:
  get:
    $ref: "./user/user/paths/GetUserCapacities.yaml"
/user/{userId}/progress:
  get:
    parameters:
      - $ref: 'parameters/path/UserId.yaml'
      - $ref: 'parameters/query/OrganizationCodeQueryOptional.yaml'
    summary: Get user profile progress
    operationId: getUserProfileProgress
    responses:
      200:
        description: erhgo occupation behaviors categories after update
        content:
          application/json:
            schema:
              $ref: 'user/user/schemas/UserProfileProgress.yaml'
/user/{userId}/registration-state:
  get:
    $ref: "./user/user/paths/GetUserRegistrationState.yaml"
/user/behavior/details/{userId}:
  get:
    $ref: "./user/behavior/paths/GetUserBehaviorDetails.yaml"
/user/behavior/{userId}:
  put:
    $ref: "./user/behavior/paths/UpdateUserBehaviors.yaml"
/user/{userId}/level:
  get:
    $ref: "./user/user/paths/GetUserLevel.yaml"
/user/initialize-profile:
  put:
    $ref: "./user/user/paths/InitializeProfile.yaml"
/user/update-users-channels:
  put:
    $ref: "./user/user/paths/UpdateUsersChannels.yaml"
/user/clear-caches:
  post:
    $ref: "./user/user/paths/ClearCaches.yaml"
/user/registration-state/update-step:
  post:
    $ref: "./user/user/paths/UpdateUserRegistrationStateStep.yaml"
/user/job-offers-opt-in/{userId}:
  get:
    $ref: "./user/user/paths/GetUserJobOffersOptIn.yaml"
/user/job-offers-opt-in:
  post:
    $ref: "./user/user/paths/UpdateUserJobOffersOptIn.yaml"
/user/job-dating-opt-in/{userId}:
  get:
    $ref: "./user/user/paths/GetUserJobDatingNotifyOptIn.yaml"
/user/job-dating-opt-in:
  post:
    $ref: "./user/user/paths/UpdateUserJobDatingNotifyOptIn.yaml"
/user/handicap-mode-enabled/{userId}:
  get:
    $ref: "./user/user/paths/GetUserHandicapModeEnabled.yaml"
/user/handicap-mode-enabled:
  put:
    $ref: "./user/user/paths/UpdateUserHandicapModeEnabled.yaml"
/user/{userId}/profile-competences:
  get:
    $ref: "./user/user/paths/GetUserProfileFOPdf.yaml"
/user/{userId}/note:
  get:
    $ref: "./user/user/paths/GetUserNotes.yaml"
/user/save-note:
  post:
    $ref: "./user/user/paths/SaveUserNote.yaml"
/user/delete-note:
  post:
    $ref: "./user/user/paths/DeleteUserNote.yaml"
/user/reindex-all:
  post:
    $ref: "./user/user/paths/ReindexAllUsers.yaml"
/user/index-now:
  post:
    $ref: "./user/user/paths/IndexUsersNow.yaml"
/user/{userId}/email:
  post:
    $ref: "./user/user/paths/SetUserEmail.yaml"
/user/soft-skills/{userId}/pdf:
  get:
    $ref: "./user/soft-skills/GetUserSoftSkillsPdfResult.yaml"
/user/force-behavior-description:
  post:
    $ref: "./user/user/paths/ForceUserBehaviorDescription.yaml"
/user/{userId}/behavior-description:
  get:
    $ref: "./user/user/paths/GetUserBehaviorDescription.yaml"
/user/{userId}/hashtags:
  get:
    $ref: "./user/user/paths/GetUserHashtags.yaml"
/user/{userId}/generate-experiences-from-cv:
  post:
    $ref: "./user/user/paths/GenerateExperiencesFromCV.yaml"
/user/{userId}/get-file-import-state:
  get:
    $ref: "./user/user/paths/GetUserFileImportState.yaml"
/user/profile-completion:
  get:
    $ref: "./user/user/paths/GetUserProfileCompletion.yaml"
/user/bulk-cv-processing:
  post:
    $ref: "./user/user/paths/CreateOrUpdateProfilesForCVs.yaml"



/notification/user/{userId}:
  get:
    $ref: "./user/user/paths/GetUserNotifications.yaml"
/notification/user/{userId}/read-all:
  get:
    $ref: "./user/user/paths/MarkAllNotificationsAsRead.yaml"
/notification/{notificationId}/read:
  post:
    $ref: "./user/user/paths/MarkNotificationAsRead.yaml"
/notification/{notificationId}/delete:
  delete:
    $ref: "./user/user/paths/DeleteUserNotification.yaml"



/user-mobile/save-token:
  post:
    $ref: "./user/mobile/paths/SaveUserMobileToken.yaml"
/user-mobile/send-notifications:
  post:
    $ref: "./user/mobile/paths/SendUserMobileNotification.yaml"
/user-mobile/notification/count:
  get:
    $ref: "./user/notification/paths/CountNotifiableUsers.yaml"
/user-mobile/notification/send:
  post:
    $ref: "./user/notification/paths/SendNotificationsToUsersSelection.yaml"




/candidature/publish:
  post:
    $ref: "./candidature/job/paths/PublishCandidature.yaml"
/candidature/{candidatureId}/contextsToEvaluate:
  get:
    $ref: "./candidature/job/paths/GetContextsToEvaluateReferencingExperiences.yaml"
/candidature/{candidatureId}/contextsMet:
  post:
    $ref: "./candidature/job/paths/SetContextsMet.yaml"
/candidature/{recruitmentId}/saveCustomAnswer:
  post:
    $ref: "./candidature/job/paths/SaveCustomAnswer.yaml"
/candidature/{candidatureId}/candidatureNote/save:
  post:
    $ref: "./candidature/job/paths/SaveCandidatureNote.yaml"
/candidature/refuse:
  post:
    $ref: "./candidature/job/paths/RefuseCandidature.yaml"
/candidature/generate:
  post:
    $ref: "./candidature/job/paths/GenerateCandidaturesOnRecruitments.yaml"
/candidature/for-job/{jobId}/preview-for-user/{userId}:
  get:
    $ref: "./candidature/job/paths/GetCandidaturePreview.yaml"
/candidature/availability/{recruitmentId}:
  post:
    $ref: "./candidature/job/paths/UpdateAvailability.yaml"
/candidature/for-job/{recruitmentId}/initial-data:
  get:
    $ref: "./candidature/job/paths/InitializeCandidatureData.yaml"
/candidature/for-organization/{organizationCode}/sectors:
  put:
    $ref: "./candidature/spontaneous/paths/UpdateSectorForSpontaneousCandidature.yaml"
/candidature/common/export:
  get:
    $ref: "./candidature/common/paths/ExportCandidatures.yaml"


/statistics/candidatures:
  get:
    $ref: "./statistics/candidate/paths/GetMonthlyCandidaturesStats.yaml"
/statistics/spontaneous-candidatures:
  get:
    $ref: "./statistics/candidate/paths/GetMonthlySpontaneousCandidaturesStats.yaml"
/statistics/recruitments:
  get:
    $ref: "./statistics/recruitment/paths/GetRecruitmentStats.yaml"
/statistics/several-metrics:
  get:
    $ref: "./statistics/several-metrics/paths/GetSeveralMetricsStats.yaml"


/behavior/list:
  get:
    $ref: "./referential/behavior/paths/GetBehaviorPage.yaml"


/landing-page:
  put:
    $ref: "./landing-page/paths/SaveLandingPage.yaml"
/landing-page/{uuid}:
  get:
    $ref: "./landing-page/paths/GetLandingPage.yaml"
/landing-page/list:
  get:
    $ref: "./landing-page/paths/GetLandingPagePage.yaml"


/organization:
  put:
    $ref: "./organization/paths/SaveOrganization.yaml"
/organization/for-id/{id}:
  get:
    $ref: "./organization/paths/GetOrganization.yaml"
/organization/all-recruiters-for-organization:
  get:
    $ref: "./organization/paths/GetAllRecruitersForOrganization.yaml"
/organization/recruiter/all:
  get:
    $ref: "./organization/paths/GetAllRecruiters.yaml"
/organization/for-code/{organizationCode}:
  get:
    $ref: "./organization/paths/GetOrganizationByCode.yaml"
/organization/codes:
  get:
    $ref: "./organization/paths/GetAllOrganizationsByCodes.yaml"
/organization/list:
  get:
    $ref: "./organization/paths/FindOrganizationsPaginatedAndFilteredByProperty.yaml"
/organization/{recruiterCode}/reindex-recruitments:
  post:
    $ref: "./organization/paths/ReindexRecruiterRecruitments.yaml"


/criteria/list:
  get:
    $ref: "./criteria/paths/GetCriteria.yaml"
/criteria/edit/{code}:
  post:
    $ref: "./criteria/paths/EditCriteria.yaml"



/work-environment/list:
  get:
    $ref: "./referential/work-environment/paths/GetWorkEnvironments.yaml"


/data-health-check/execute-queries:
  get:
    $ref: "./data-health-check/ExecuteAllHealthCheckQueries.yaml"


/manual-execution/execute-task:
  post:
    $ref: "./misc/paths/ExecuteBackgroundTask.yaml"
/manual-execution/sync-ats:
  post:
    $ref: "./misc/paths/SyncAts.yaml"


/erhgo-classification/list:
  get:
    $ref: "./classifications/erhgo/paths/GetErhgoClassification.yaml"


/sourcing/recruitment/form-submission:
  post:
    $ref: "./sourcing/paths/CreateOrUpdateRecruitmentByFormSubmission.yaml"
/sourcing/recruitment/erhgo-classifications:
  put:
    $ref: "./sourcing/paths/UpdateErhgoClassificationsForSourcingRecruitment.yaml"
/sourcing/count-candidates:
  get:
    $ref: "./sourcing/paths/CountCandidates.yaml"
/sourcing/recruitment/{recruitmentId}/count-matching-users:
  get:
    $ref: "./sourcing/paths/CountMatchingUsers.yaml"
/sourcing/initialize-account:
  post:
    $ref: "./sourcing/paths/InitializeAccount.yaml"
/sourcing/job/{jobId}/contract:
  post:
    $ref: "./sourcing/paths/UpdateJobContract.yaml"
/sourcing/job/{jobId}/criteria:
  put:
    $ref: "./sourcing/paths/UpdateCriteriaForSourcingJob.yaml"
/sourcing/recruitment/{recruitmentId}/question:
  put:
    $ref: "./sourcing/paths/UpdateSourcingRecruitmentQuestion.yaml"
/sourcing/recruitment/{recruitmentId}/detail:
  get:
    $ref: "./sourcing/paths/GetSourcingJobAndRecruitment.yaml"
/sourcing/recruitments:
  get:
    $ref: "./sourcing/paths/GetSourcingRecruitments.yaml"
/sourcing/users/{organizationCode}:
  get:
    $ref: "./sourcing/paths/GetSourcingUsers.yaml"
/sourcing/users-to-notify:
  put:
    $ref: "./sourcing/paths/UpdateUsersToNotify.yaml"
/sourcing/manager:
  put:
    $ref: "./sourcing/paths/UpdateManager.yaml"
/sourcing/spontaneous-candidature/{candidatureId}/soft-skills-pdf:
  get:
    $ref: "./sourcing/paths/GetSoftSkillsPdfResult.yaml"
/sourcing/spontaneous-candidature/users-to-notify:
  get:
    $ref: "./sourcing/paths/GetUsersToNotifyOnSpontaneousCandidature.yaml"
/sourcing/{recruitmentId}/step:
  put:
    $ref: "./sourcing/paths/UpdateSourcingStep.yaml"
/sourcing/candidature/{candidatureId}/profile-competences:
  get:
    $ref: "./sourcing/paths/ExportUserProfileCompetences.yaml"
/sourcing/candidatures:
  get:
    $ref: "./sourcing/paths/GetSourcingCandidaturesPage.yaml"
/sourcing/invitation-codes:
  get:
    $ref: "./sourcing/paths/ListSourcingInvitationCode.yaml"
/sourcing/invitation-code:
  post:
    $ref: "./sourcing/paths/SaveInvitationCodeSourcing.yaml"
/sourcing/{candidatureId}/state:
  put:
    $ref: "./sourcing/paths/UpdateSourcingCandidatureState.yaml"
/sourcing/recruitment-candidature/{candidatureId}/detail:
  get:
    $ref: "./sourcing/paths/GetSourcingCandidatureDetail.yaml"
/sourcing/organization:
  get:
    $ref: "./sourcing/paths/GetSourcingOrganization.yaml"
  put:
    $ref: "./sourcing/paths/UpdateSourcingOrganization.yaml"
/sourcing/invite-to-recruitment:
  post:
    $ref: "./sourcing/paths/InviteToRecruitment.yaml"
/sourcing/contact:
  post:
    $ref: "./sourcing/paths/SendContactForm.yaml"
/sourcing/recruitment:
  post:
    $ref: "./sourcing/paths/CreateOrUpdateSourcingJobAndRecruitment.yaml"
/sourcing/recruitment/duplicate:
  post:
    $ref: "./sourcing/paths/DuplicateSourcingJobAndRecruitment.yaml"
/sourcing/invite-and-create-new-user:
  post:
    $ref: "./sourcing/paths/InviteAndCreateNewUser.yaml"
/sourcing/recruitment/{recruitmentId}/change-state:
  post:
    $ref: "./sourcing/paths/ChangeRecruitmentState.yaml"
/sourcing/simulate-filters:
  get:
    $ref: "./sourcing/paths/SimulateSourcingFilters.yaml"
/sourcing/{id}/similar-recruitments:
  get:
    $ref: "./sourcing/paths/GetSimilarRecruitments.yaml"
/sourcing/subscription:
  get:
    $ref: "./sourcing/paths/GetSourcingSubscription.yaml"
  post:
    $ref: "./sourcing/paths/UpdateSubscription.yaml"
/sourcing/user:
  get:
    $ref: "./sourcing/paths/GetSourcingUser.yaml"
  put:
    $ref: "./sourcing/paths/UpdateSourcingUser.yaml"



/sector/list:
  get:
    $ref: "./sector/paths/GetSectors.yaml"
